// Copyright Isto Inc.

using UnityEngine;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Interface for all enemy controllers to provide common functionality
    /// </summary>
    public interface IEnemyController
    {
        /// <summary>
        /// The GameObject this enemy controller is attached to
        /// </summary>
        GameObject GameObject { get; }

        /// <summary>
        /// The Transform of this enemy
        /// </summary>
        Transform Transform { get; }

        /// <summary>
        /// Whether this enemy is currently active and alive
        /// </summary>
        bool IsActive { get; }

        /// <summary>
        /// The current target this enemy is pursuing (if any)
        /// </summary>
        Transform CurrentTarget { get; }

        /// <summary>
        /// Activate or deactivate this enemy
        /// </summary>
        /// <param name="active">Whether to activate the enemy</param>
        void SetActive(bool active);

        /// <summary>
        /// Kill this enemy
        /// </summary>
        void Kill();

        /// <summary>
        /// Set a new target for this enemy to pursue
        /// </summary>
        /// <param name="target">The target transform</param>
        void SetTarget(Transform target);

        /// <summary>
        /// Clear the current target
        /// </summary>
        void ClearTarget();
    }
}