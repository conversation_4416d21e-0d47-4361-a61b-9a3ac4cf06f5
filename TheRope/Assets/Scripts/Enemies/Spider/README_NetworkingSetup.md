# Spider State Machine Networking Setup

## Overview

The Spider enemy has been updated with Master Client Authority networking to synchronize state machine logic across all PUN2 clients. This ensures consistent behavior and eliminates the random patrol point desynchronization issue.

## What Was Changed

### 1. SpiderController Updates

- **Added PUN2 Integration**: Implements `IPunObservable` interface
- **Master Client Authority**: Only the master client makes state decisions
- **Network State Synchronization**: State changes are broadcast via RPCs and continuous sync
- **Deterministic Target Selection**: Replaced `Random.Range()` with deterministic selection based on time/position

### 2. State Machine Updates

- **EnemyPatrolState**: Only master client selects new patrol targets
- **EnemyIdleState**: Only master client makes state transition decisions
- **EnemyChaseState**: Only master client handles target detection and chase logic
- **All States**: Non-master clients receive and apply state changes from master client

### 3. Key Networking Features

- **RPC State Changes**: Immediate state synchronization via `OnStateChanged` RPC
- **Continuous Sync**: `OnPhotonSerializeView` provides ongoing state synchronization
- **Target Index Sync**: Patrol points are synchronized by index rather than Transform references
- **Deterministic Random**: Uses seeded random generation for consistent "random" selections

## Setup Instructions

### 1. PhotonView Configuration

On your Spider prefab, ensure the PhotonView component is configured:

```
PhotonView Settings:
- View ID: (Auto-assigned)
- Ownership Transfer: Fixed
- Observed Components:
  1. Photon Transform View (for position/rotation)
  2. Spider Controller (for state machine sync)
```

### 2. Photon Transform View Settings

```
Synchronize Options:
- Position: ✓ Enabled
- Rotation: ✓ Enabled  
- Scale: ✗ Disabled (unless needed)
- Use Local: ✓ Enabled (recommended)
```

### 3. Spider Controller Observable Settings

In the PhotonView's "Observed Components" list:
- Add the SpiderController component
- This enables the `OnPhotonSerializeView` method for state synchronization

## How It Works

### Master Client Authority

1. **Master Client**: Makes all state machine decisions (transitions, target selection)
2. **Other Clients**: Receive and apply state changes from master client
3. **Automatic Failover**: If master client leaves, PUN2 automatically assigns a new master

### State Synchronization

1. **Immediate Sync**: State changes are sent via RPC for instant updates
2. **Continuous Sync**: `OnPhotonSerializeView` ensures ongoing synchronization
3. **Target Sync**: Patrol point targets are synchronized by index for consistency

### Deterministic Behavior

- **Seeded Random**: Uses time + position hash for consistent "random" selections
- **Index-Based Targets**: Patrol points referenced by index, not Transform
- **Network-Safe Logic**: All decision logic only runs on master client

## Expected Behavior

### Before (Desynchronized)
- Host: "Spider is Patrolling. Moving to target: PatrolPoint2"
- Client: "Spider is Patrolling. Moving to target: PatrolPoint4"

### After (Synchronized)
- Host: "Spider is Patrolling. Moving to target: PatrolPoint2"
- Client: "Spider is Patrolling. Moving to target: PatrolPoint2"

## Testing

1. **Single Player**: Should work exactly as before
2. **Multiplayer Host**: Master client controls state machine
3. **Multiplayer Client**: Receives state updates from master client
4. **Master Client Switch**: New master client takes over seamlessly

## Performance Considerations

- **Minimal Network Traffic**: Only state changes and essential data are synchronized
- **Efficient RPCs**: State changes sent only when needed
- **Optimized Sync**: `OnPhotonSerializeView` sends minimal data (3 values)

## Troubleshooting

### Common Issues

1. **States Not Syncing**: Check PhotonView has SpiderController in Observed Components
2. **Random Behavior**: Ensure only master client makes decisions in state logic
3. **Target Desync**: Verify patrol points are found consistently on all clients

### Debug Information

- Enable Spider debugging to see state messages
- Check console for "Spider changing state" and "Spider received state change" logs
- Verify master client status with `PhotonNetwork.IsMasterClient`

## Future Enhancements

- **Player Target Sync**: Synchronize player targets for chase state
- **Animation Sync**: Add animation state synchronization
- **Health/Damage Sync**: Synchronize spider health and damage states
- **Performance Optimization**: Reduce sync frequency for distant spiders
