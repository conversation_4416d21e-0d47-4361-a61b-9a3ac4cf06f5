// Copyright Isto Inc.

using UnityEngine;
using Photon.Pun;

namespace Isto.TRP.Enemies
{
    /// <summary>
    /// Debug helper for Spider networking issues
    /// </summary>
    public class SpiderNetworkDebugger : MonoBehaviourPun
    {
        [Header("Debug Settings")]
        [SerializeField] private bool _enableDebugLogs = true;
        [SerializeField] private float _debugInterval = 2f;

        private SpiderController _spiderController;
        private float _lastDebugTime;

        private void Start()
        {
            _spiderController = GetComponent<SpiderController>();
            if (_spiderController == null)
            {
                Debug.LogError("SpiderNetworkDebugger: No SpiderController found on this GameObject!");
                enabled = false;
            }
        }

        private void Update()
        {
            if (!_enableDebugLogs || _spiderController == null)
                return;

            if (Time.time - _lastDebugTime >= _debugInterval)
            {
                LogNetworkState();
                _lastDebugTime = Time.time;
            }
        }

        private void LogNetworkState()
        {
            string prefix = _spiderController.IsMasterClient ? "[MASTER]" : "[CLIENT]";
            string currentStateName = _spiderController.GetCurrentState()?.GetType().Name ?? "NULL";
            string targetName = _spiderController.TargetTransform?.name ?? "NULL";

            Debug.Log($"{prefix} Spider Network State: " +
                     $"Current={currentStateName}, " +
                     $"Networked={_spiderController.NetworkedCurrentState}, " +
                     $"Target={targetName}, " +
                     $"IsNetworked={_spiderController.IsNetworked}");
        }

        [ContextMenu("Force State Sync")]
        public void ForceStateSync()
        {
            if (_spiderController != null && _spiderController.IsMasterClient)
            {
                Debug.Log("Forcing state sync from master client...");
                _spiderController.ChangeState(_spiderController.NetworkedCurrentState);
            }
            else
            {
                Debug.Log("Cannot force sync - not master client or no spider controller");
            }
        }

        [ContextMenu("Log Current State")]
        public void LogCurrentState()
        {
            if (_spiderController != null)
            {
                Debug.Log($"Spider Debug Info: {_spiderController.NetworkDebugInfo}");
            }
        }
    }
}