// Copyright Isto Inc.

using UnityEngine;
using Photon.Pun;

namespace Isto.TRP.Enemies
{
    /// <summary>
    /// Debug helper for Spider networking issues
    /// </summary>
    public class SpiderNetworkDebugger : MonoBehaviourPun
    {
        [Header("Debug Settings")]
        [SerializeField] private bool _enableDebugLogs = true;
        [SerializeField] private float _debugInterval = 1f;

        private SpiderController _spiderController;
        private float _lastDebugTime;

        private void Start()
        {
            _spiderController = GetComponent<SpiderController>();
            if (_spiderController == null)
            {
                Debug.LogError("SpiderNetworkDebugger: No SpiderController found on this GameObject!");
                enabled = false;
            }
        }

        private void Update()
        {
            if (!_enableDebugLogs || _spiderController == null)
                return;

            if (Time.time - _lastDebugTime >= _debugInterval)
            {
                LogNetworkState();
                _lastDebugTime = Time.time;
            }
        }

        private void LogNetworkState()
        {
            string prefix = _spiderController.IsMasterClient ? "[MASTER]" : "[CLIENT]";
            string currentStateName = _spiderController.GetCurrentState()?.GetType().Name ?? "NULL";
            string targetName = _spiderController.TargetTransform?.name ?? "NULL";

            Debug.Log($"{prefix} Spider Network State: " +
                     $"Current={currentStateName}, " +
                     $"Networked={_spiderController.NetworkedCurrentState}, " +
                     $"Target={targetName}, " +
                     $"TargetIndex={(_spiderController.TargetTransform != null && _spiderController.PatrolPoints != null ? _spiderController.PatrolPoints.IndexOf(_spiderController.TargetTransform) : -1)}, " +
                     $"IsNetworked={_spiderController.IsNetworked}");
        }

        [ContextMenu("Force State Sync")]
        public void ForceStateSync()
        {
            if (_spiderController != null && _spiderController.IsMasterClient)
            {
                Debug.Log("Forcing state sync from master client...");
                // Force the master to broadcast its current state
                var currentState = _spiderController.NetworkedCurrentState;
                _spiderController.ChangeState(currentState);
            }
            else
            {
                Debug.Log("Cannot force sync - not master client or no spider controller");
            }
        }

        [ContextMenu("Force Patrol State")]
        public void ForcePatrolState()
        {
            if (_spiderController != null && _spiderController.IsMasterClient)
            {
                Debug.Log("Forcing patrol state from master client...");
                _spiderController.ChangeState(SpiderController.EnemyEnum.Patrol);
            }
            else
            {
                Debug.Log("Cannot force patrol - not master client or no spider controller");
            }
        }

        [ContextMenu("Log Current State")]
        public void LogCurrentState()
        {
            if (_spiderController != null)
            {
                Debug.Log($"Spider Debug Info: {_spiderController.NetworkDebugInfo}");
            }
        }
    }
}