// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.StateMachine;
using RootMotion.Demos;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Photon.Pun;
using System.Collections;

namespace Isto.TRP.Enemies
{
    public class SpiderController : CoreEnemyController, IPunObservable
    {
        // UNITY HOOKUP

        [Header("-- SPIDER DEBUG --")]
        [SerializeField] private Canvas _debugBillboard;
        [SerializeField] private TextMeshProUGUI _debugEnemyStateText;
        [SerializeField] private bool _isDebugging = false;

        [Header("Spider Settings")]
        [SerializeField] private MechSpider _mechSpider;
        [SerializeField] private float _speed     = 6f;
        [SerializeField] private float _turnSpeed = 60f;

        [Header("State Setup")]
        [SerializeField] private EnemyIdleState _idleState;
        [SerializeField] private EnemyPatrolState _patrolState;
        [SerializeField] private EnemyChaseState _chaseState;
        [SerializeField] private EnemyAttackState _attackState;
        [SerializeField] private EnemyRetreatState _retreatState;
        [SerializeField] private EnemyScarePlayerState _scarePlayerState;
        [SerializeField] private EnemyDeadState _deadState;

        [Header("Spider Variables")]
        [Tooltip("Which tag to look for when hunting players")]
        [SerializeField] private string _playerTag = "Player";
        [Tooltip("How far the spider can 'see'")]
        [SerializeField] private float _detectionRadius = 60;
        [SerializeField] private float _loseSightDistance = 85f;
        [SerializeField] private float _attackDistance    = 15f;
        [SerializeField] private float _boredDuration = 10f;
        [SerializeField] private float _idleDuration = 5f;
        [SerializeField] private float _patrolDuration = 45f;

        public enum EnemyEnum
        {
            Idle,
            Patrol,
            Chase,
            Attack,
            Retreat,
            ScarePlayer,
            Dead
        }

        // Used for debugging purposes
        private Transform _localPlayerCamera;
        private float _lastCameraSearchTime;
        private const float CAMERA_SEARCH_INTERVAL = 1f; // Search for camera every 1 second if not found

        private Dictionary<EnemyEnum, MonoState> _stateMap;
        private List<Transform> _patrolPoints;
        private Transform _target;
        private Transform _playerTarget; // Separate field for tracking player targets
        private bool _isAttacking = false;
        private Transform _patrolPointContainer;

        // NETWORKING FIELDS
        private PhotonView _photonView;
        private EnemyEnum _networkedCurrentState = EnemyEnum.Idle;
        private int _networkedTargetIndex = -1; // Index of current target in patrol points list
        private float _networkedStateTimer = 0f;
        private bool _isNetworkInitialized = false;


        public Transform TargetTransform => _target;

        public bool IsDebugging => _isDebugging;
        public float DistanceToTarget => _target != null ? Vector3.Distance(transform.position, _target.position) : 0f;
        public float LoseSightDistance => _loseSightDistance;
        public float AttackDistance => _attackDistance;
        public float BoredDuration => _boredDuration;
        public float IdleDuration => _idleDuration;
        public bool IsAttacking => _isAttacking;
        public float PatrolDuration => _patrolDuration;
        public List<Transform> PatrolPoints => _patrolPoints;

        // NETWORKING PROPERTIES
        public bool IsMasterClient => _photonView != null ? _photonView.IsMine && PhotonNetwork.IsMasterClient : true;
        public bool IsNetworked => _photonView != null && PhotonNetwork.IsConnected;
        public EnemyEnum NetworkedCurrentState => _networkedCurrentState;


        // UNITY LIFECYCLE

        // build the map once
        protected override void Awake()
        {
            base.Awake();

            // Initialize networking
            _photonView = GetComponent<PhotonView>();

            _stateMap = new Dictionary<EnemyEnum, MonoState>
            {
                { EnemyEnum.Idle,           _idleState   },
                { EnemyEnum.Patrol,         _patrolState },
                { EnemyEnum.Chase,          _chaseState  },
                { EnemyEnum.Attack,         _attackState },
                { EnemyEnum.Retreat,        _retreatState},
                { EnemyEnum.ScarePlayer,    _scarePlayerState},
                { EnemyEnum.Dead,           _deadState   },
            };

            _patrolPointContainer = GameObject.Find("c_PatrolPoints").transform;
            _patrolPoints = new List<Transform>();
            foreach (Transform child in _patrolPointContainer)
            {
                _patrolPoints.Add(child);
            }

            _debugBillboard.gameObject.SetActive(_isDebugging);

            // Initialize camera search
            if (_isDebugging)
            {
                FindLocalPlayerCamera();
            }
        }

        protected override void Start()
        {
            base.Start();

            // Initialize network state after all components are ready
            if (IsNetworked)
            {
                if (IsMasterClient)
                {
                    // Master client starts in Idle state
                    _networkedCurrentState = EnemyEnum.Idle;
                    ChangeState(EnemyEnum.Idle);
                }
                else
                {
                    // Non-master clients wait for state sync
                    StartCoroutine(WaitForNetworkInitialization());
                }
            }
            else
            {
                // Single player mode - start normally
                ChangeState(EnemyEnum.Idle);
            }

            _isNetworkInitialized = true;
        }

        private IEnumerator WaitForNetworkInitialization()
        {
            // Wait a short time for initial network sync
            yield return new WaitForSeconds(0.5f);

            // If we still haven't received a state, default to Idle
            if (_currentState == null)
            {
                ChangeState(EnemyEnum.Idle);
            }
        }

        // run the state‐machine first, then do movement
        protected override void Update()
        {
            if (IsDebugging)
            {
                DrawDetectionRays();
                var currentState = _currentState as EnemyState;
                _debugEnemyStateText.text = currentState?.DebugStateMessage;
            }

            base.Update();

            if(_target == null || _isAttacking)
            {
                return;
            }

            Vector3 dir = (_target.position - transform.position).normalized;
            if (dir.sqrMagnitude > 0f)
            {
                Quaternion lookRot = Quaternion.LookRotation(dir);
                transform.rotation = Quaternion.RotateTowards(
                    transform.rotation,
                    lookRot,
                    Time.deltaTime * _turnSpeed
                );
                transform.Translate(
                    Vector3.forward * _speed * Time.deltaTime * _mechSpider.scale,
                    Space.Self
                );

                if(IsDebugging)
                {
                    // Have the billboard always face the local player's camera
                    UpdateBillboardRotation();
                }
            }
        }

        public void ChangeState(EnemyEnum newState)
        {
            // Only master client can initiate state changes in networked mode
            if (IsNetworked && !IsMasterClient)
            {
                return;
            }

            Debug.Log($"Spider changing state to {newState} from {_currentState?.GetType().Name ?? "null"}");

            _networkedCurrentState = newState;
            ChangeState(_stateMap[newState]);

            // Broadcast state change to other clients
            if (IsNetworked && IsMasterClient)
            {
                _photonView.RPC("OnStateChanged", RpcTarget.Others, (int)newState, _networkedTargetIndex, _networkedStateTimer);
            }
        }

        [PunRPC]
        private void OnStateChanged(int stateInt, int targetIndex, float stateTimer)
        {
            EnemyEnum newState = (EnemyEnum)stateInt;
            _networkedCurrentState = newState;
            _networkedTargetIndex = targetIndex;
            _networkedStateTimer = stateTimer;

            // Apply the target if valid
            if (targetIndex >= 0 && targetIndex < _patrolPoints.Count)
            {
                SetTarget(_patrolPoints[targetIndex]);
            }

            // Change to the new state
            ChangeState(_stateMap[newState]);

            Debug.Log($"Spider received state change: {newState}, target index: {targetIndex}");
        }

        public MonoState GetState(EnemyEnum newState)
        {
            return _stateMap[newState];
        }

        /// <summary>
        /// Scans all GameObjects with tag=playerTag, picks the closest one within detectionRadius.
        /// Does not overwrite the current target - only stores the player target separately.
        /// </summary>
        public bool TryFindNearestPlayerWithinRadius()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = _detectionRadius * _detectionRadius;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 <= bestSqr)
                {
                    bestSqr = d2;
                    best    = go.transform;
                }
            }
            _playerTarget = best;
            return best != null;
        }

        public void FindNearestPlayer()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = float.MaxValue;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 < bestSqr)
                {
                    bestSqr = d2;
                    best    = go.transform;
                }
            }
            _playerTarget = best;
            _target = best; // Set the main target to the player when actively finding them
        }

        public void SetTarget(Transform newTarget)
        {
            _target = newTarget;

            // Update networked target index if this is a patrol point
            if (newTarget != null && _patrolPoints != null)
            {
                _networkedTargetIndex = _patrolPoints.IndexOf(newTarget);
            }
            else
            {
                _networkedTargetIndex = -1;
            }
        }

        /// <summary>
        /// Sets target by patrol point index - used for network synchronization
        /// </summary>
        public void SetTargetByIndex(int patrolPointIndex)
        {
            if (patrolPointIndex >= 0 && patrolPointIndex < _patrolPoints.Count)
            {
                SetTarget(_patrolPoints[patrolPointIndex]);
            }
            else
            {
                SetTarget(null);
            }
        }

        /// <summary>
        /// Gets a deterministic patrol point index for network synchronization
        /// </summary>
        public int GetRandomPatrolPointIndex(int excludeIndex = -1)
        {
            if (_patrolPoints == null || _patrolPoints.Count == 0)
                return -1;

            if (_patrolPoints.Count == 1)
                return 0;

            // Use a deterministic method based on current time and position
            // This ensures all clients get the same "random" result
            int seed = Mathf.FloorToInt(Time.time * 1000) + transform.position.GetHashCode();
            UnityEngine.Random.State oldState = UnityEngine.Random.state;
            UnityEngine.Random.InitState(seed);

            List<int> availableIndices = new List<int>();
            for (int i = 0; i < _patrolPoints.Count; i++)
            {
                if (i != excludeIndex)
                {
                    availableIndices.Add(i);
                }
            }

            int randomIndex = availableIndices.Count > 0 ?
                availableIndices[UnityEngine.Random.Range(0, availableIndices.Count)] : 0;

            UnityEngine.Random.state = oldState;
            return randomIndex;
        }

        /// <summary>
        /// Sets the target to the currently detected player if one exists.
        /// Used when transitioning from patrol to chase state.
        /// </summary>
        public void SetTargetToPlayer()
        {
            if (_playerTarget != null)
            {
                _target = _playerTarget;
            }
        }

        private void DrawDetectionRays()
        {
            int rayCount = 36;
            float step = 360f / rayCount;

            for (int i = 0; i < rayCount; i++)
            {
                float angle = step * i * Mathf.Deg2Rad;
                Vector3 dir = new Vector3(Mathf.Cos(angle), 0f, Mathf.Sin(angle));
                Debug.DrawRay(
                    transform.position,
                    dir * _detectionRadius,
                    Color.red
                );
            }
        }

        /// <summary>
        /// Finds and caches the local player's camera transform for billboard rotation.
        /// This method searches for the local player (PhotonView.IsMine == true) and gets their camera.
        /// </summary>
        private void FindLocalPlayerCamera()
        {
            // Only search if we don't have a camera reference or enough time has passed
            if (_localPlayerCamera != null || Time.time - _lastCameraSearchTime < CAMERA_SEARCH_INTERVAL)
            {
                return;
            }

            _lastCameraSearchTime = Time.time;

            // Find all TRPPlayerController instances in the scene
            TRPPlayerController[] playerControllers = FindObjectsOfType<TRPPlayerController>();

            foreach (TRPPlayerController playerController in playerControllers)
            {
                // Check if this is the local player
                if (playerController.IsMine)
                {
                    // Get the camera transform from the local player
                    _localPlayerCamera = playerController.CameraRoot;
                    break;
                }
            }

            // Fallback to Camera.main if no local player found (for testing or single-player scenarios)
            if (_localPlayerCamera == null && Camera.main != null)
            {
                _localPlayerCamera = Camera.main.transform;
            }
        }

        /// <summary>
        /// Updates the billboard rotation to face the local player's camera.
        /// Uses smooth rotation for better visual quality and performance.
        /// </summary>
        private void UpdateBillboardRotation()
        {
            // Ensure we have a camera reference
            if (_localPlayerCamera == null)
            {
                FindLocalPlayerCamera();
                return;
            }

            // Calculate direction from billboard to camera
            Vector3 directionToCamera = (_localPlayerCamera.position - _debugBillboard.transform.position).normalized;

            // Create rotation that looks at the camera
            Quaternion targetRotation = Quaternion.LookRotation(directionToCamera);

            // Apply smooth rotation for better visual quality
            _debugBillboard.transform.rotation = Quaternion.Slerp(
                _debugBillboard.transform.rotation,
                targetRotation,
                Time.deltaTime * 5f // Smooth rotation speed
            );
        }

        public void SetIsAttacking(bool isAttacking)
        {
            _isAttacking = isAttacking;
        }


        // CORE ENEMY CONTROLLER OVERRIDES

        protected override void OnKilled()
        {
            base.OnKilled();
            ChangeState(EnemyEnum.Dead);
        }

        protected override void OnRespawned()
        {
            base.OnRespawned();
            ChangeState(EnemyEnum.Idle);
        }

        protected override void OnTargetChanged(Transform newTarget)
        {
            base.OnTargetChanged(newTarget);
            _target = newTarget;

            // If we have a new target and we're not dead, start chasing
            if (newTarget != null && !IsDead)
            {
                ChangeState(EnemyEnum.Chase);
            }
        }

#if UNITY_EDITOR
        // Draw a wireframe sphere in the editor
        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, _detectionRadius);
        }
#endif


        // IPUNOBSERVABLE IMPLEMENTATION

        public void OnPhotonSerializeView(PhotonStream stream, PhotonMessageInfo info)
        {
            if (stream.IsWriting)
            {
                // Send data to other clients
                stream.SendNext((int)_networkedCurrentState);
                stream.SendNext(_networkedTargetIndex);
                stream.SendNext(_networkedStateTimer);
            }
            else
            {
                // Receive data from other clients
                int stateInt = (int)stream.ReceiveNext();
                int targetIndex = (int)stream.ReceiveNext();
                float stateTimer = (float)stream.ReceiveNext();

                // Only apply if we're not the master client
                if (!IsMasterClient && _isNetworkInitialized)
                {
                    EnemyEnum newState = (EnemyEnum)stateInt;

                    // Only change state if it's different
                    if (_networkedCurrentState != newState)
                    {
                        _networkedCurrentState = newState;
                        _networkedTargetIndex = targetIndex;
                        _networkedStateTimer = stateTimer;

                        // Apply the target if valid
                        if (targetIndex >= 0 && targetIndex < _patrolPoints.Count)
                        {
                            SetTarget(_patrolPoints[targetIndex]);
                        }

                        // Change to the new state
                        ChangeState(_stateMap[newState]);
                    }
                    else
                    {
                        // Update target and timer even if state is the same
                        _networkedTargetIndex = targetIndex;
                        _networkedStateTimer = stateTimer;

                        if (targetIndex >= 0 && targetIndex < _patrolPoints.Count && _target != _patrolPoints[targetIndex])
                        {
                            SetTarget(_patrolPoints[targetIndex]);
                        }
                    }
                }
            }
        }
    }
}