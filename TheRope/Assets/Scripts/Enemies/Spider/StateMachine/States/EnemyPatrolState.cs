// Copyright Isto Inc.

using Isto.Core.StateMachine;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyPatrolState : EnemyState
    {
        // OTHER FIELDS

        private float _timer;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _timer = 0f;

            // Only master client selects new patrol targets
            if (!_spiderController.IsNetworked || _spiderController.IsMasterClient)
            {
                SetRandomPatrolTarget();
            }

            UpdateDebugMessage();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            _timer += Time.deltaTime;

            // Only master client makes decisions in networked mode
            if (_spiderController.IsNetworked && !_spiderController.IsMasterClient)
            {
                UpdateDebugMessage();
                return this;
            }

            // Example transition: after patrolDuration, go to chase (or idle)
            if (_timer >= _spiderController.PatrolDuration)
            {
                return _spiderController.GetState(SpiderController.EnemyEnum.Idle);
            }

            // Check for players within detection radius
            if (_spiderController.TryFindNearestPlayerWithinRadius())
            {
                _spiderController.SetTargetToPlayer(); // Switch target to the detected player
                return _spiderController.GetState(SpiderController.EnemyEnum.Chase);
            }

            // Check if we need a new patrol target
            if (_spiderController.TargetTransform == null || _spiderController.DistanceToTarget <= 10f)
            {
                FindNewPatrolTarget();
            }

            UpdateDebugMessage();
            return this;
        }

        private void FindNewPatrolTarget()
        {
            var patrolPoints = _spiderController.PatrolPoints;
            if (patrolPoints == null || patrolPoints.Count == 0)
                return;

            if (patrolPoints.Count == 1)
            {
                // If there's only one patrol point, just stay there
                _spiderController.SetTargetByIndex(0);
            }
            else
            {
                // Get current target index to exclude it
                int currentIndex = _spiderController.TargetTransform != null ?
                    patrolPoints.IndexOf(_spiderController.TargetTransform) : -1;

                // Use deterministic random selection for network consistency
                int newIndex = _spiderController.GetRandomPatrolPointIndex(currentIndex);
                _spiderController.SetTargetByIndex(newIndex);
            }
        }

        private void SetRandomPatrolTarget()
        {
            if (_spiderController.PatrolPoints != null && _spiderController.PatrolPoints.Count > 0)
            {
                // Use deterministic random selection for network consistency
                int randomIndex = _spiderController.GetRandomPatrolPointIndex();
                _spiderController.SetTargetByIndex(randomIndex);
            }
        }

        private void UpdateDebugMessage()
        {
            if (_spiderController.TargetTransform != null)
            {
                _debugStateMessage = "Spider is Patrolling. Moving to target: " + _spiderController.TargetTransform.name;
            }
            else
            {
                _debugStateMessage = "Spider is Patrolling. No target set.";
            }
        }

        public override void Exit(MonoStateMachine controller)
        {
            // cleanup if needed
        }
    }
}